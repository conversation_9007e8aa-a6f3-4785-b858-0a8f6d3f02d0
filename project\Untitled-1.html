<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Générateur et Scanner de Code-Barres - Aptiv</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="icon"
        href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📊</text></svg>">
</head>

<body>
    <div class="container">
        <!-- Header -->
        <header>
            <h1>🔍 Générateur de Code-Barres</h1>
            <p>Créez et scannez des codes-barres facilement</p>
        </header>

        <!-- Input Section -->
        <div class="input-group">
            <input type="text" id="barcodeInput" placeholder="Entrez un texte ou un numéro (ex: 1234567890123)"
                maxlength="50">
            <div class="button-group">
                <button id="generateButton">
                    <span>📊</span> Générer Code-Barres
                </button>
                <button onclick="loadSampleData()" style="background: #17a2b8; color: white;">
                    <span>🎲</span> Données Test
                </button>
            </div>

            <!-- Category-specific data buttons -->
            <div class="button-group" style="margin-top: 15px;">
                <button onclick="loadCategoryData('aptivProducts')"
                    style="background: #28a745; color: white; font-size: 12px; padding: 8px 12px;">
                    🔧 Produits Aptiv
                </button>
                <button onclick="loadCategoryData('standardProducts')"
                    style="background: #ffc107; color: black; font-size: 12px; padding: 8px 12px;">
                    📦 Produits Standard
                </button>
                <button onclick="loadCategoryData('internalCodes')"
                    style="background: #6f42c1; color: white; font-size: 12px; padding: 8px 12px;">
                    🏷️ Codes Internes
                </button>
                <button onclick="loadCategoryData('testData')"
                    style="background: #fd7e14; color: white; font-size: 12px; padding: 8px 12px;">
                    🧪 Tests
                </button>
            </div>
        </div>

        <!-- Format Selection (will be populated by JavaScript) -->
        <div class="button-group"></div>

        <!-- Error Message -->
        <div class="error-message"></div>

        <!-- Barcode Display -->
        <div class="barcode-container">
            <svg id="barcode"></svg>
        </div>

        <!-- Barcode Info -->
        <div class="barcode-info"></div>

        <!-- Download Button -->
        <button id="downloadButton">
            <span>💾</span> Télécharger PNG
        </button>

        <!-- Scanner Section -->
        <div class="scanner-section">
            <h2>📷 Scanner de Code-Barres</h2>
            <p>Utilisez votre caméra pour scanner des codes-barres</p>

            <div id="cameraContainer">
                <video id="camera" autoplay playsinline></video>
                <div class="scanner-overlay"></div>
            </div>

            <div class="button-group">
                <button id="startScanButton">
                    <span>📸</span> Démarrer Scan
                </button>
                <button id="stopScanButton">
                    <span>⏹️</span> Arrêter Scan
                </button>
            </div>

            <div class="scan-result"></div>
        </div>

        <!-- Instructions -->
        <div style="margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 10px; text-align: left;">
            <h3>📋 Instructions d'utilisation:</h3>
            <ul style="margin: 15px 0; padding-left: 20px;">
                <li><strong>Génération:</strong> Entrez votre texte et sélectionnez le format désiré</li>
                <li><strong>Formats supportés:</strong> Code 128, Code 39, EAN-13, EAN-8, UPC-A, ITF-14</li>
                <li><strong>Téléchargement:</strong> Cliquez sur "Télécharger PNG" pour sauvegarder</li>
                <li><strong>Scanner:</strong> Utilisez la caméra pour détecter des codes-barres</li>
                <li><strong>Données test:</strong> Cliquez sur "Données Test" pour des exemples</li>
            </ul>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script src="script.js"></script>
</body>

</html>