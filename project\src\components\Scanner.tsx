import React, { useState, useRef, useEffect } from 'react';
import { Camera, Square, Play, Pause, RotateCcw, Upload, CheckCircle, AlertCircle } from 'lucide-react';
import BarcodeGenerator from './BarcodeGenerator';

interface ScanResult {
  data: string;
  format: string;
  timestamp: Date;
  confidence?: number;
}

const Scanner: React.FC = () => {
  const [scanResult, setScanResult] = useState<ScanResult | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState('');
  const [scanHistory, setScanHistory] = useState<ScanResult[]>([]);
  const [activeTab, setActiveTab] = useState<'scanner' | 'generator'>('scanner');
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Simulate barcode scanning (replace with actual library like QuaggaJS or ZXing)
  const simulateBarcodeDetection = () => {
    const sampleBarcodes = [
      { data: '1234567890123', format: 'EAN13' },
      { data: 'APTIV-CABLE-001', format: 'CODE128' },
      { data: '9780123456789', format: 'EAN13' },
      { data: 'HELLO WORLD', format: 'CODE39' },
      { data: '123456789012', format: 'UPC' }
    ];

    const randomBarcode = sampleBarcodes[Math.floor(Math.random() * sampleBarcodes.length)];
    const result: ScanResult = {
      ...randomBarcode,
      timestamp: new Date(),
      confidence: Math.random() * 0.3 + 0.7 // 70-100% confidence
    };

    setScanResult(result);
    setScanHistory(prev => [result, ...prev.slice(0, 9)]); // Keep last 10 scans
    setIsScanning(false);
  };

  const startScanning = async () => {
    try {
      setError('');
      setIsScanning(true);

      // Request camera access
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: 'environment' } // Use back camera if available
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }

      // Simulate detection after 3 seconds
      setTimeout(() => {
        if (isScanning) {
          simulateBarcodeDetection();
        }
      }, 3000);

    } catch (err: any) {
      setError('Impossible d\'accéder à la caméra: ' + err.message);
      setIsScanning(false);
    }
  };

  const stopScanning = () => {
    setIsScanning(false);

    if (videoRef.current?.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      videoRef.current.srcObject = null;
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Simulate barcode detection from image
    const reader = new FileReader();
    reader.onload = () => {
      // In a real implementation, you would process the image here
      setTimeout(() => {
        simulateBarcodeDetection();
      }, 1000);
    };
    reader.readAsDataURL(file);
  };

  const handleManualInput = () => {
    const input = prompt("Entrez manuellement les données du code-barres:");
    if (input?.trim()) {
      const result: ScanResult = {
        data: input.trim(),
        format: 'MANUAL',
        timestamp: new Date(),
        confidence: 1.0
      };
      setScanResult(result);
      setScanHistory(prev => [result, ...prev.slice(0, 9)]);
    }
  };

  const clearResults = () => {
    setScanResult(null);
    setScanHistory([]);
    setError('');
  };

  useEffect(() => {
    return () => {
      stopScanning();
    };
  }, []);

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Tab Navigation */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('scanner')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'scanner'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
            >
              <Camera className="w-5 h-5 inline mr-2" />
              Scanner de Code-Barres
            </button>
            <button
              onClick={() => setActiveTab('generator')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'generator'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
            >
              <Square className="w-5 h-5 inline mr-2" />
              Générateur de Code-Barres
            </button>
          </nav>
        </div>
      </div>

      {activeTab === 'scanner' ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Scanner Section */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
              <Camera className="mr-2 text-blue-600" />
              Scanner de Code-Barres
            </h2>

            {/* Camera View */}
            <div className="relative mb-4">
              <div className="bg-gray-900 rounded-lg overflow-hidden aspect-video">
                <video
                  ref={videoRef}
                  className="w-full h-full object-cover"
                  autoPlay
                  playsInline
                  muted
                />
                {isScanning && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="border-2 border-red-500 w-3/4 h-1/2 rounded-lg animate-pulse">
                      <div className="absolute top-0 left-0 w-6 h-6 border-t-2 border-l-2 border-red-500"></div>
                      <div className="absolute top-0 right-0 w-6 h-6 border-t-2 border-r-2 border-red-500"></div>
                      <div className="absolute bottom-0 left-0 w-6 h-6 border-b-2 border-l-2 border-red-500"></div>
                      <div className="absolute bottom-0 right-0 w-6 h-6 border-b-2 border-r-2 border-red-500"></div>
                    </div>
                  </div>
                )}
                {!isScanning && (
                  <div className="absolute inset-0 flex items-center justify-center text-white">
                    <div className="text-center">
                      <Camera className="w-16 h-16 mx-auto mb-2 opacity-50" />
                      <p>Caméra inactive</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Control Buttons */}
            <div className="grid grid-cols-2 gap-3 mb-4">
              <button
                onClick={isScanning ? stopScanning : startScanning}
                className={`px-4 py-3 rounded-lg font-medium transition-colors flex items-center justify-center ${isScanning
                    ? 'bg-red-600 hover:bg-red-700 text-white'
                    : 'bg-blue-600 hover:bg-blue-700 text-white'
                  }`}
              >
                {isScanning ? (
                  <>
                    <Pause className="w-5 h-5 mr-2" />
                    Arrêter
                  </>
                ) : (
                  <>
                    <Play className="w-5 h-5 mr-2" />
                    Scanner
                  </>
                )}
              </button>

              <button
                onClick={() => fileInputRef.current?.click()}
                className="px-4 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors flex items-center justify-center"
              >
                <Upload className="w-5 h-5 mr-2" />
                Image
              </button>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <button
                onClick={handleManualInput}
                className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors"
              >
                Saisie Manuelle
              </button>

              <button
                onClick={clearResults}
                className="px-4 py-2 bg-gray-400 hover:bg-gray-500 text-white rounded-lg font-medium transition-colors flex items-center justify-center"
              >
                <RotateCcw className="w-4 h-4 mr-1" />
                Effacer
              </button>
            </div>

            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileUpload}
              className="hidden"
            />

            {/* Error Message */}
            {error && (
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center">
                <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}
          </div>

          {/* Results Section */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-bold text-gray-900 mb-4">Résultats du Scan</h3>

            {/* Current Result */}
            {scanResult && (
              <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center mb-2">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
                  <h4 className="font-medium text-green-900">Dernier Scan</h4>
                </div>
                <div className="space-y-2 text-sm">
                  <div>
                    <span className="font-medium">Données:</span>
                    <span className="ml-2 font-mono bg-white px-2 py-1 rounded border">
                      {scanResult.data}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium">Format:</span>
                    <span className="ml-2">{scanResult.format}</span>
                  </div>
                  <div>
                    <span className="font-medium">Heure:</span>
                    <span className="ml-2">{scanResult.timestamp.toLocaleString('fr-FR')}</span>
                  </div>
                  {scanResult.confidence && (
                    <div>
                      <span className="font-medium">Confiance:</span>
                      <span className="ml-2">{Math.round(scanResult.confidence * 100)}%</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Scan History */}
            {scanHistory.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Historique des Scans</h4>
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {scanHistory.map((scan, index) => (
                    <div key={index} className="p-3 bg-gray-50 rounded border text-sm">
                      <div className="font-mono text-gray-900">{scan.data}</div>
                      <div className="text-gray-500 text-xs mt-1">
                        {scan.format} • {scan.timestamp.toLocaleTimeString('fr-FR')}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {!scanResult && scanHistory.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <Square className="w-12 h-12 mx-auto mb-3 opacity-50" />
                <p>Aucun code-barres scanné</p>
                <p className="text-sm">Utilisez la caméra ou uploadez une image</p>
              </div>
            )}
          </div>
        </div>
      ) : (
        <BarcodeGenerator onBarcodeGenerated={(data, format) => {
          console.log('Barcode generated:', data, format);
        }} />
      )}
    </div>
  );
};

export default Scanner;
