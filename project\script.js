// Barcode Generator and Scanner
class BarcodeManager {
    constructor() {
        this.currentFormat = 'CODE128';
        this.scanner = null;
        this.isScanning = false;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.createFormatButtons();
        this.setupCamera();
    }

    setupEventListeners() {
        const generateButton = document.getElementById('generateButton');
        const barcodeInput = document.getElementById('barcodeInput');
        const downloadButton = document.getElementById('downloadButton');
        const startScanButton = document.getElementById('startScanButton');
        const stopScanButton = document.getElementById('stopScanButton');

        generateButton?.addEventListener('click', () => this.generateBarcode());
        barcodeInput?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.generateBarcode();
        });
        downloadButton?.addEventListener('click', () => this.downloadBarcode());
        startScanButton?.addEventListener('click', () => this.startScanning());
        stopScanButton?.addEventListener('click', () => this.stopScanning());

        // Auto-generate on input change
        barcodeInput?.addEventListener('input', () => {
            if (barcodeInput.value.trim()) {
                this.generateBarcode();
            }
        });
    }

    createFormatButtons() {
        const buttonGroup = document.querySelector('.button-group');
        if (!buttonGroup) return;

        const formats = [
            { code: 'CODE128', name: 'Code 128' },
            { code: 'CODE39', name: 'Code 39' },
            { code: 'EAN13', name: 'EAN-13' },
            { code: 'EAN8', name: 'EAN-8' },
            { code: 'UPC', name: 'UPC-A' },
            { code: 'ITF14', name: 'ITF-14' }
        ];

        formats.forEach(format => {
            const button = document.createElement('button');
            button.className = `format-button ${format.code === this.currentFormat ? 'active' : ''}`;
            button.textContent = format.name;
            button.addEventListener('click', () => this.setFormat(format.code, button));
            buttonGroup.appendChild(button);
        });
    }

    setFormat(format, button) {
        this.currentFormat = format;
        
        // Update active button
        document.querySelectorAll('.format-button').forEach(btn => {
            btn.classList.remove('active');
        });
        button.classList.add('active');

        // Regenerate barcode if input has value
        const input = document.getElementById('barcodeInput');
        if (input?.value.trim()) {
            this.generateBarcode();
        }
    }

    generateBarcode() {
        const input = document.getElementById('barcodeInput');
        const barcodeElement = document.getElementById('barcode');
        const errorElement = document.querySelector('.error-message');
        const infoElement = document.querySelector('.barcode-info');
        const downloadButton = document.getElementById('downloadButton');

        if (!input || !barcodeElement) return;

        const text = input.value.trim();
        if (!text) {
            this.showError('Veuillez entrer un texte ou un numéro');
            return;
        }

        try {
            // Clear previous content
            barcodeElement.innerHTML = '';
            this.hideError();

            // Validate input based on format
            if (!this.validateInput(text, this.currentFormat)) {
                this.showError(`Format invalide pour ${this.currentFormat}`);
                return;
            }

            // Generate barcode
            JsBarcode(barcodeElement, text, {
                format: this.currentFormat,
                width: 2,
                height: 100,
                displayValue: true,
                fontSize: 14,
                margin: 10,
                background: '#ffffff',
                lineColor: '#000000'
            });

            // Show barcode info
            this.showBarcodeInfo(text, this.currentFormat);
            
            // Show download button
            if (downloadButton) {
                downloadButton.style.display = 'inline-block';
            }

        } catch (error) {
            this.showError('Erreur lors de la génération du code-barres: ' + error.message);
        }
    }

    validateInput(text, format) {
        switch (format) {
            case 'EAN13':
                return /^\d{12,13}$/.test(text);
            case 'EAN8':
                return /^\d{7,8}$/.test(text);
            case 'UPC':
                return /^\d{11,12}$/.test(text);
            case 'CODE39':
                return /^[A-Z0-9\-. $/+%]*$/.test(text);
            case 'ITF14':
                return /^\d{13,14}$/.test(text);
            case 'CODE128':
            default:
                return text.length > 0;
        }
    }

    showBarcodeInfo(text, format) {
        const infoElement = document.querySelector('.barcode-info');
        if (!infoElement) return;

        infoElement.innerHTML = `
            <h3>Informations du Code-Barres</h3>
            <p><strong>Texte:</strong> ${text}</p>
            <p><strong>Format:</strong> ${format}</p>
            <p><strong>Longueur:</strong> ${text.length} caractères</p>
            <p><strong>Généré le:</strong> ${new Date().toLocaleString('fr-FR')}</p>
        `;
        infoElement.style.display = 'block';
    }

    showError(message) {
        const errorElement = document.querySelector('.error-message');
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }
    }

    hideError() {
        const errorElement = document.querySelector('.error-message');
        if (errorElement) {
            errorElement.style.display = 'none';
        }
    }

    downloadBarcode() {
        const barcodeElement = document.getElementById('barcode');
        if (!barcodeElement) return;

        try {
            // Create canvas from SVG
            const svg = barcodeElement.outerHTML;
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = function() {
                canvas.width = img.width;
                canvas.height = img.height;
                ctx.drawImage(img, 0, 0);

                // Download
                const link = document.createElement('a');
                link.download = `barcode_${Date.now()}.png`;
                link.href = canvas.toDataURL();
                link.click();
            };

            img.src = 'data:image/svg+xml;base64,' + btoa(svg);
        } catch (error) {
            this.showError('Erreur lors du téléchargement: ' + error.message);
        }
    }

    setupCamera() {
        // This is a placeholder for camera setup
        // In a real implementation, you would use libraries like QuaggaJS or ZXing
        console.log('Camera setup for barcode scanning');
    }

    async startScanning() {
        const startButton = document.getElementById('startScanButton');
        const stopButton = document.getElementById('stopScanButton');
        const cameraContainer = document.getElementById('cameraContainer');

        if (!startButton || !stopButton) return;

        try {
            startButton.style.display = 'none';
            stopButton.style.display = 'inline-block';
            this.isScanning = true;

            // Simulate camera access (replace with actual camera implementation)
            this.simulateScanning();

        } catch (error) {
            this.showError('Erreur d\'accès à la caméra: ' + error.message);
            this.stopScanning();
        }
    }

    stopScanning() {
        const startButton = document.getElementById('startScanButton');
        const stopButton = document.getElementById('stopScanButton');

        if (startButton) startButton.style.display = 'inline-block';
        if (stopButton) stopButton.style.display = 'none';
        
        this.isScanning = false;

        if (this.scanner) {
            this.scanner.stop();
            this.scanner = null;
        }
    }

    simulateScanning() {
        // This simulates barcode detection
        // Replace with actual barcode scanning library
        setTimeout(() => {
            if (this.isScanning) {
                const simulatedResult = '1234567890123';
                this.onBarcodeDetected(simulatedResult);
            }
        }, 3000);
    }

    onBarcodeDetected(result) {
        const scanResult = document.querySelector('.scan-result');
        const barcodeInput = document.getElementById('barcodeInput');

        if (scanResult) {
            scanResult.innerHTML = `
                <h3>Code-Barres Détecté!</h3>
                <p><strong>Résultat:</strong> ${result}</p>
                <p><strong>Détecté le:</strong> ${new Date().toLocaleString('fr-FR')}</p>
            `;
            scanResult.style.display = 'block';
        }

        if (barcodeInput) {
            barcodeInput.value = result;
            this.generateBarcode();
        }

        this.stopScanning();
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new BarcodeManager();
});

// Sample barcode data for testing
const sampleBarcodes = [
    '1234567890123',
    '9780123456789',
    'HELLO WORLD',
    '12345678',
    '123456789012'
];

// Add sample data functionality
function loadSampleData() {
    const input = document.getElementById('barcodeInput');
    if (input) {
        const randomBarcode = sampleBarcodes[Math.floor(Math.random() * sampleBarcodes.length)];
        input.value = randomBarcode;
        
        // Trigger generation
        const event = new Event('input');
        input.dispatchEvent(event);
    }
}
